<template>
  <PageContainer :footer="true">
    <div slot="content" class="form-content">
      <div class="form-title"><i class="el-icon-arrow-left" @click="$router.go(-1)"></i>{{ pageTitle }}</div>
      <div class="content-main">
        <!-- 基础信息表单 -->
        <div class="main-title">基础信息</div>
        <el-form ref="formRef" :model="formModel" :rules="rules" label-width="130px">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="名称" prop="name">
                <el-input v-model="formModel.name" placeholder="请输入名称" maxlength="10" show-word-limit clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否出入口" prop="isEntrance">
                <el-radio-group v-model="formModel.isEntrance">
                  <el-radio v-for="item in yesNoOptions" :key="item.value" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
                <el-tooltip effect="dark" content="出入口为医院主要大门的出入口，选为出入口后人流量不计入所在楼层的计算。" placement="top">
                  <i class="el-icon-question" style="margin-left: 8px; color: #909399; cursor: help"></i>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所在楼层" prop="floorId">
                <el-select v-model="formModel.floorId" placeholder="请选择所在楼层" clearable style="width: 100%" :loading="floorLoading">
                  <el-option v-for="item in floorOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <!-- 设备 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="进入设备" name="enter"></el-tab-pane>
          <el-tab-pane label="离开设备" name="exit"></el-tab-pane>
        </el-tabs>
        <div class="device-section">
          <div class="device-actions">
            <el-button type="primary" icon="el-icon-plus" @click="handleAddDevice"> 添加设备 </el-button>
          </div>
          <TablePage ref="exitTable" :table-column="deviceTableColumns" :data="activeTab === 'enter' ? enterDevices : exitDevices" :show-page="false" height="calc(100% - 50px)">
            <template #actions="{ row, index }">
              <el-button type="text" size="small" style="color: #f56c6c" @click="handleDeleteDevice(row, index)"> 移除 </el-button>
            </template>
          </TablePage>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button style="padding: 8px 22px" @click="$router.go(-1)">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="submitForm">确认</el-button>
    </div>
  </PageContainer>
</template>
<script>
var pageTitle
export default {
  name: 'add',
  // 路由前置守卫动态修改meta中的title
  beforeRouteEnter(to, from, next) {
    if (to.meta.title) {
      const typeList = {
        add: '新增',
        edit: '编辑'
      }
      pageTitle = typeList[to.query.type] ?? '新增'
      to.meta.title = pageTitle
    }
    next()
  },
  data() {
    return {
      pageTitle,
      // 表单数据
      formModel: {
        name: '', // 名称
        isEntrance: 0, // 是否出入口，默认为否
        floorId: '' // 所在楼层ID
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' },
          { max: 10, message: '名称不能超过10个字符', trigger: 'blur' }
        ],
        isEntrance: [{ required: true, message: '请选择是否出入口', trigger: 'change' }],
        floorId: [{ required: true, message: '请选择所在楼层', trigger: 'change' }]
      },
      // 是否出入口选项
      yesNoOptions: [
        { value: 0, label: '否' },
        { value: 1, label: '是' }
      ],
      // 楼层选项
      floorOptions: [],
      floorLoading: false,
      // 当前激活的tab
      activeTab: 'enter',
      // 进入设备列表
      enterDevices: [],
      // 离开设备列表
      exitDevices: [],
      // 设备表格列配置
      deviceTableColumns: [
        { prop: 'deviceName', label: '设备名称', minWidth: 150 },
        { slot: 'actions', label: '操作', width: 180, fixed: 'right' }
      ],
      // 提交loading状态
      submitLoading: false
    }
  },
  computed: {},
  created() {
    // 初始化数据
    this.initData()
  },
  methods: {
    /**
     * 初始化数据
     */
    initData() {
      // 获取楼层列表
      this.getFloorList()
      // 如果是编辑模式，获取详情数据
      if (this.$route.query.type === 'edit' && this.$route.query.id) {
        this.getDetailData()
      }
    },
    /**
     * 获取楼层列表 - 预留接口
     * TODO: 对接楼层列表API
     */
    async getFloorList() {
      try {
        this.floorLoading = true
        // 预留API调用
        // const res = await this.$api.getFloorList()
        // if (res.code === '200') {
        //   this.floorOptions = res.data || []
        // }
        // 模拟数据，实际开发时删除
        this.floorOptions = [
          { id: '1', name: '1楼' },
          { id: '2', name: '2楼' },
          { id: '3', name: '3楼' },
          { id: '4', name: '4楼' },
          { id: '5', name: '5楼' }
        ]
      } catch (error) {
        this.$message.error('获取楼层列表失败')
        console.error('获取楼层列表失败:', error)
      } finally {
        this.floorLoading = false
      }
    },
    /**
     * 获取详情数据 - 预留接口
     * TODO: 对接详情数据API
     */
    async getDetailData() {
      try {
        const id = this.$route.query.id
        // 预留API调用
        // const res = await this.$api.getPeopleFlowMonitorDetail({ id })
        // if (res.code === '200') {
        //   const data = res.data
        //   this.formModel = {
        //     name: data.name,
        //     isEntrance: data.isEntrance,
        //     floorId: data.floorId
        //   }
        //   this.enterDevices = data.enterDevices || []
        //   this.exitDevices = data.exitDevices || []
        // }
        console.log('获取详情数据，ID:', id)
      } catch (error) {
        this.$message.error('获取详情数据失败')
        console.error('获取详情数据失败:', error)
      }
    },
    // Tab切换处理
    handleTabClick(tab) {
      this.activeTab = tab.name
    },
    // 添加设备
    handleAddDevice() {
      // TODO: 打开设备选择弹窗
      // 模拟添加设备数据，实际开发时删除
      const mockDevice = {
        id: Date.now(),
        deviceCode: `DEV${Date.now()}`,
        deviceName: `测试设备${Date.now()}`,
        deviceType: '人流监测设备',
        location: '测试位置',
        status: 1
      }
      // 验证设备是否已存在
      if (this.isDeviceExists(mockDevice.id, this.activeTab)) {
        this.$message.warning('该设备已添加，不能重复添加')
        return
      }
      if (this.activeTab === 'enter') {
        this.enterDevices.push(mockDevice)
      } else {
        this.exitDevices.push(mockDevice)
      }
    },
    // 删除设备
    handleDeleteDevice(row, index) {
      if (this.activeTab === 'enter') {
        this.enterDevices.splice(index, 1)
      } else {
        this.exitDevices.splice(index, 1)
      }
    },
    // 检查设备是否已存在
    isDeviceExists(deviceId) {
      const devices = this.activeTab === 'enter' ? this.enterDevices : this.exitDevices
      return devices.some((device) => device.id === deviceId)
    },
    /**
     * 验证设备数据
     * @returns {boolean}
     */
    validateDevices() {
      // 检查是否至少添加了一个设备
      if (this.enterDevices.length === 0 && this.exitDevices.length === 0) {
        this.$message.warning('请至少添加一个设备')
        return false
      }
      // 检查设备重复性（同一设备不能在进入和离开中同时存在）
      const enterDeviceIds = this.enterDevices.map((d) => d.id)
      const exitDeviceIds = this.exitDevices.map((d) => d.id)
      const duplicateIds = enterDeviceIds.filter((id) => exitDeviceIds.includes(id))
      if (duplicateIds.length > 0) {
        this.$message.warning('同一设备不能同时作为进入设备和离开设备')
        return false
      }
      return true
    },
    /**
     * 提交表单
     */
    async submitForm() {
      try {
        // 验证基础表单
        const valid = await this.$refs.formRef.validate()
        if (!valid) {
          return
        }
        // 验证设备数据
        if (!this.validateDevices()) {
          return
        }
        this.submitLoading = true
        // 构建提交数据
        const submitData = {
          ...this.formModel,
          enterDevices: this.enterDevices,
          exitDevices: this.exitDevices
        }
        // TODO: 调用保存API
        // const res = await this.$api.savePeopleFlowMonitor(submitData)
        // if (res.code === '200') {
        //   this.$message.success('保存成功')
        //   this.$router.go(-1)
        // } else {
        //   throw new Error(res.message || '保存失败')
        // }
        console.log('提交数据:', submitData)
        // 模拟API调用延迟
        await new Promise((resolve) => setTimeout(resolve, 1000))
        this.$message.success('保存成功')
        this.$router.go(-1)
      } catch (error) {
        this.$message.error(error.message || '保存失败')
        console.error('提交表单失败:', error)
      } finally {
        this.submitLoading = false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.form-content {
  height: 100%;
  overflow-y: auto;
  .form-title {
    height: 40px;
    line-height: 40px;
    background: #fff;
    border-bottom: 1px solid #dcdfe6;
    font-size: 15px;
    font-family: 'PingFang SC-Medium', 'PingFang SC';
    font-weight: 500;
    color: #121f3e;
    padding-left: 15px;
    i {
      width: 20px;
      cursor: pointer;
    }
  }
  .content-main {
    height: calc(100% - 40px);
    width: 100%;
    background: #fff;
    padding: 24px 48px;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    .main-title {
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      margin-bottom: 24px;
    }
  }
}
// 设备管理样式
.device-section {
  flex: 1;
  .device-actions {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-start;
  }
}
// 表单项样式优化
::v-deep .el-form-item {
  margin-bottom: 20px;
}
// 提示图标样式
.el-icon-question {
  font-size: 14px;
  &:hover {
    color: #409eff;
  }
}
// Tab样式优化
::v-deep .el-tabs {
  .el-tabs__header {
    margin-bottom: 16px;
  }
  .el-tabs__content {
    padding: 0;
  }
}
// 表格样式优化
::v-deep .el-table {
  .el-button--text {
    padding: 0;
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
