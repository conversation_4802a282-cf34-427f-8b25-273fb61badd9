<template>
  <el-dialog title="请选择设备" width="1050px" :visible.sync="dialogShow" :close-on-click-modal="false" custom-class="model-dialog" :before-close="closeDialog">
    <div class="harvester_content">
      <div class="table_box">
        <div class="search_box">
          <div class="search_select">
            <span class="require-span">设备名称:</span>
            <el-input v-model="searchForm.assetsName" placeholder="请输入设备名称" style="width: 200px"></el-input>
            <span class="require-span" style="margin-left: 10px">所属品类:</span>
            <el-select ref="sysOfcode" v-model="searchForm.sysOfcode" clearable placeholder="选择品类" @clear="handleClear1">
              <el-option hidden :value="searchForm.sysOfcode" :label="categoryName"> </el-option>
              <el-tree :data="categoryList" :props="categoryProps" :expand-on-click-node="false" :check-on-click-node="true" @node-click="handleNodeClick1"> </el-tree>
            </el-select>
            <span class="require-span" style="margin-left: 10px">所属编码:</span>
            <el-input v-model="searchForm.factoryCode" placeholder="请输入设备SN码" style="width: 200px"></el-input>
            <div style="display: inline-block; margin-left: 10px">
              <el-button type="primary" plain @click="resetForm">重置</el-button>
              <el-button type="primary" @click="inquire">查询</el-button>
            </div>
          </div>
        </div>
        <TablePage
          ref="treeTable"
          v-loading="tableLoading"
          class="table_div"
          row-key="id"
          :showPage="true"
          :tableColumn="tableColumn"
          :data="tableData"
          default-expand-all
          height="calc(100% - 280px)"
          :pageData="pageData"
          :pageProps="pageProps"
          @pagination="paginationChange"
        >
        </TablePage>
        <!-- ---------------------------- -->
        <div class="parameter_box">
          <div class="check_box_list">
            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">全选</el-checkbox>
            <div v-if="propertiesList.length">
              <div style="display: flex">
                属性： <el-checkbox v-model="checkAllProperties" :indeterminate="isIndeterminateProperties" @change="handleCheckAllPropertiesChange">全选</el-checkbox>
              </div>
              <el-checkbox-group v-model="checkParameterList.properties">
                <el-checkbox v-for="(item, index) in propertiesList" :key="index" :label="item.id" :value="item" @change="handleproPertiesSelect(item)">{{
                  item.metadataName
                }}</el-checkbox>
              </el-checkbox-group>
            </div>
            <div v-if="functionsList.length">
              <div style="display: flex">
                功能： <el-checkbox v-model="checkAllFunctions" :indeterminate="isIndeterminateFunctions" @change="handleCheckAllFunctionsChange">全选</el-checkbox>
              </div>
              <el-checkbox-group v-model="checkParameterList.functions">
                <el-checkbox v-for="(item, index) in functionsList" :key="index" :label="item.id" :value="item" @change="handleFunctionsSelect(item)">{{
                  item.metadataName
                }}</el-checkbox>
              </el-checkbox-group>
            </div>
            <div v-if="eventsList.length">
              <div style="display: flex">
                事件： <el-checkbox v-model="checkAllEvents" :indeterminate="isIndeterminateEvents" @change="handleCheckAllEventsChange">全选</el-checkbox>
              </div>
              <el-checkbox-group v-model="checkParameterList.events">
                <el-checkbox v-for="(item, index) in eventsList" :key="index" :label="item.id" :value="item" @change="handleEventSelect(item)">{{ item.metadataName }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <!-- ---------------------------- -->
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="closeDialog">取 消</el-button>
      <el-button type="primary" @click="groupSubmit">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script lang="jsx">
import { transData } from '@/util'
export default {
  name: 'selectMonitorDevice',
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    dialogData: {
      type: Object,
      default: () => {}
    },
    tableDataList: {
      type: Array,
      default: () => []
    },
    harvesterCheckType: {
      // 选中类型为 单选还是多选
      type: String,
      default: 'radio'
    }
  },
  data() {
    return {
      systemCodeList: [], // 所属品类
      parameterList: [], // 物联checkBox列表
      searchForm: {
        assetsName: '',
        factoryCode: ''
      },
      categoryName: '', // 品类name
      categoryList: [], // 品类list
      category: [], // 品类list
      categoryProps: {
        label: 'dictionaryDetailsName',
        isLeaf: 'dictionaryDetailsCode',
        children: 'children'
      },
      tableData: [],
      tableLoading: false,
      multipleCheckData: [], // 多选数据
      disabledIds: [],
      checkRadio: '',
      pageData: {
        current: 1,
        size: 15,
        total: 0
      },
      pageProps: {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      },
      selectedRows: {}, // 选中的行
      propertiesList: [], // 属性
      eventsList: [], // 事件
      functionsList: [], // 功能
      checkParameterList: {
        properties: [],
        functions: [],
        events: []
      },
      checkAll: false,
      isIndeterminate: false,
      checkAllProperties: false,
      isIndeterminateProperties: false,
      checkAllFunctions: false,
      isIndeterminateFunctions: false,
      checkAllEvents: false,
      isIndeterminateEvents: false,
      propertiesMap: [],
      functionsMap: [],
      eventsMap: []
    }
  },
  computed: {
    tableColumn() {
      return [
        {
          label: '',
          width: 80,
          align: 'center',
          render: (h, row) => {
            return (
              <el-radio
                v-model={this.checkRadio}
                label={row.row.id}
                onChange={() => {
                  this.harvesterListRadioCheck(row.row)
                }}
              >
                &nbsp;
              </el-radio>
            )
          },
          hasJudge: this.harvesterCheckType == 'radio'
        },
        {
          type: 'selection',
          align: 'center',
          width: 80,
          hasJudge: this.harvesterCheckType == 'checkbox'
        },
        {
          prop: 'assetsName',
          label: '设备名称',
          required: true
        },
        {
          prop: 'factoryCode',
          label: '设备SN',
          required: true
        },
        {
          prop: 'sysOf1Name',
          label: '设备类型'
        }
      ]
    }
  },
  mounted() {
    this.getSensorList()
    this.systemType()
    this.disabledIds = this.tableDataList.map((item) => `${item.factoryCode}${item.metadataTag}`)
  },
  methods: {
    // 重置
    resetForm() {
      this.pageData = {
        current: 1,
        size: 15,
        total: 0
      }
      this.pageProps = {
        page: 'current',
        pageSize: 'size',
        total: 'total'
      }
      this.searchForm = {
        assetsName: '',
        factoryCode: ''
      }
      this.categoryName = ''
      this.multipleCheckData = []
      this.parameterList = []
      this.tableData = []
      this.getSensorList()
    },
    // 查询
    inquire() {
      this.multipleCheckData = []
      this.parameterList = []
      this.tableData = []
      this.getSensorList()
    },
    // 所属品类
    systemType() {
      let data = {
        page: 1,
        pageSize: 200,
        enable: 1
      }
      this.$api.getCategoryManagementList(data).then((res) => {
        if (res.code == '200') {
          this.category = res.data.records
          res.data.records.map((e) => {
            e.leaf = false
          })
          this.categoryList = transData(res.data.records, 'dictionaryDetailsCode', 'parentId', 'children')
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 选择品类下拉树 数据
    handleNodeClick1(data) {
      if (data.level == 1) {
        this.searchForm.sysOfcode = data.dictionaryDetailsCode
        this.searchForm.sysOf1code = ''
      } else {
        const parentCode = this.getParentCode(this.categoryList, data.dictionaryDetailsCode)
        if (parentCode) {
          this.searchForm.sysOfcode = parentCode
        }
        this.searchForm.sysOf1code = data.dictionaryDetailsCode
      }
      this.categoryName = data.dictionaryDetailsName
      this.$refs.sysOfcode.blur()
    },
    getParentCode(data, childCode) {
      for (const node of data) {
        if (node.children && node.children.length) {
          for (const child of node.children) {
            if (child.dictionaryDetailsCode === childCode) {
              return node.dictionaryDetailsCode // 返回父节点的 code
            }
          }
          const parentCode = this.getParentCode(node.children, childCode)
          if (parentCode) {
            return parentCode
          }
        }
      }
      return null // 如果没有找到父节点
    },
    // 品类数据清除
    handleClear1() {
      this.searchForm.sysOfcode = ''
      this.searchForm.sysOf1code = ''
      this.categoryName = ''
    },
    // 设备列表
    getSensorList() {
      this.tableLoading = true
      let param = {
        ...this.searchForm,
        pageSize: this.pageData.size,
        page: this.pageData.current
      }
      this.$api.getOperationalMonitoringQuery(param).then((res) => {
        this.tableLoading = false
        if (res.code == '200') {
          this.tableData = res.data.records
          this.pageData.total = res.data.total
        } else {
          this.tableData = []
          this.parameterList = []
        }
      })
    },
    paginationChange(pagination) {
      Object.assign(this.pageData, pagination)
      this.getSensorList()
    },
    closeDialog() {
      this.$emit('closeDialog')
    },
    // 列表单选点击
    harvesterListRadioCheck(currentRow) {
      this.selectedRows = currentRow
      this.getProperties(currentRow.id)
    },
    // 物模型
    getProperties(id) {
      this.$api.getDeviceProperties(id).then((res) => {
        if (res.code == '200') {
          this.propertiesList = []
          this.eventsList = []
          this.functionsList = []
          this.checkAll = false
          this.isIndeterminate = false
          this.checkAllProperties = false
          this.isIndeterminateProperties = false
          this.checkAllFunctions = false
          this.isIndeterminateFunctions = false
          this.checkAllEvents = false
          this.isIndeterminateEvents = false
          this.propertiesList = res.data.propertiesList
          this.eventsList = res.data.eventsList
          this.functionsList = res.data.functionsList
        }
      })
    },
    groupSubmit() {
      const parameterList = []
      // 处理属性
      if (this.checkParameterList.properties.length) {
        this.propertiesMap.forEach((e) => {
          parameterList.push({
            factoryCode: this.selectedRows.factoryCode,
            assetsName: this.selectedRows.assetsName,
            metadataType: e.metadataType,
            metadataTag: e.metadataTag,
            id: `${this.selectedRows.factoryCode}${e.metadataTag}${e.metadataType}`,
            metadataName: e.metadataName,
            valueType: e.valueType ? e.valueType : e.inputs,
            metadataValueAlias: '',
            scadaParamEqu: '',
            scadaParamSystem: '',
            isShow: 0
          })
        })
      }
      // 处理功能
      if (this.checkParameterList.functions.length) {
        this.functionsMap.forEach((e) => {
          parameterList.push({
            factoryCode: this.selectedRows.factoryCode,
            assetsName: this.selectedRows.assetsName,
            metadataType: e.metadataType,
            metadataTag: e.metadataTag,
            id: `${this.selectedRows.factoryCode}${e.metadataTag}${e.metadataType}`,
            metadataName: e.metadataName,
            valueType: e.valueType ? e.valueType : e.inputs,
            metadataValueAlias: '',
            scadaParamEqu: '',
            scadaParamSystem: '',
            isShow: 0
          })
        })
      }
      // 处理事件
      if (this.checkParameterList.events.length) {
        this.eventsMap.forEach((e) => {
          parameterList.push({
            factoryCode: this.selectedRows.factoryCode,
            assetsName: this.selectedRows.assetsName,
            metadataType: e.metadataType,
            metadataTag: e.metadataTag,
            id: `${this.selectedRows.factoryCode}${e.metadataTag}${e.metadataType}`,
            metadataName: e.metadataName,
            valueType: e.valueType ? e.valueType : e.inputs,
            metadataValueAlias: '',
            scadaParamEqu: '',
            scadaParamSystem: '',
            isShow: 0
          })
        })
      }
      console.log(parameterList, 'parameterList')
      if (parameterList.length > 0) {
        this.$emit('submitDialog', parameterList)
      } else {
        this.$message({
          message: '请选择设备！',
          type: 'warning'
        })
      }
    },
    // 属性单选
    handleproPertiesSelect() {
      this.propertiesMap = this.propertiesList.filter((property) => this.checkParameterList.properties.includes(property.id))
    },
    // 功能单选
    handleFunctionsSelect() {
      this.functionsMap = this.functionsList.filter((property) => this.checkParameterList.functions.includes(property.id))
    },
    // 事件单选
    handleEventSelect() {
      this.eventsMap = this.eventsList.filter((property) => this.checkParameterList.events.includes(property.id))
    },
    // 全选
    handleCheckAllChange(value) {
      // 全选状态改变时，更新所有类别的选中状态
      this.checkParameterList.properties = value ? this.propertiesList.map((item) => item.id) : []
      this.propertiesMap = value ? this.propertiesList.map((item) => item) : []
      this.checkParameterList.functions = value ? this.functionsList.map((item) => item.id) : []
      this.functionsMap = value ? this.functionsList.map((item) => item) : []
      this.checkParameterList.events = value ? this.eventsList.map((item) => item.id) : []
      this.eventsMap = value ? this.eventsList.map((item) => item) : []
      this.checkAllProperties = !!value
      this.checkAllFunctions = !!value
      this.checkAllEvents = !!value
      this.isIndeterminateProperties = false
      this.isIndeterminateFunctions = false
      this.isIndeterminateEvents = false
    },
    // 属性全选
    handleCheckAllPropertiesChange(value) {
      this.checkParameterList.properties = value ? this.propertiesList.map((item) => item.id) : []
      this.propertiesMap = value ? this.propertiesList.map((item) => item) : []
      this.isIndeterminateProperties = false
    },
    // 功能全选
    handleCheckAllFunctionsChange(value) {
      this.checkParameterList.functions = value ? this.functionsList.map((item) => item.id) : []
      this.functionsMap = value ? this.functionsList.map((item) => item) : []
      this.isIndeterminateFunctions = false
    },
    // 事件全选
    handleCheckAllEventsChange(value) {
      this.checkParameterList.events = value ? this.eventsList.map((item) => item.id) : []
      this.eventsMap = value ? this.eventsList.map((item) => item) : []
      this.isIndeterminateEvents = false
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  max-height: calc(78vh - 110px);
  height: calc(85vh - 110px);
  display: flex;
  padding: 15px 10px;
  overflow: auto;
}
.model-dialog {
  margin-top: 8vh !important;
  .harvester_content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .table_box {
      padding: 10px 20px 10px 10px;
      background: #fff;
      .require-span {
        span {
          color: #fa403c;
          vertical-align: middle;
        }
      }
    }
    .table_box {
      flex: 1;
      height: 0;
      display: flex;
      flex-direction: column;
      .search_box {
        .search_select {
          margin-bottom: 30px;
          > span {
            margin-right: 10px;
          }
        }
        .search_input {
          display: flex;
          align-items: center;
          width: 500px;
          margin-bottom: 15px;
          p {
            width: 90px;
            margin: 0;
            margin-right: 15px;
          }
        }
      }
      .table_div {
        height: calc(100% - 10px);
        height: 0;
      }
    }
  }
}
</style>
